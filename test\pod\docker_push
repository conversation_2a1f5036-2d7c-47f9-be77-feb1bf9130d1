#!/usr/bin/env bash

# This script builds and pushes the test-pod image

set -o errexit
set -o nounset
set -o pipefail

if ! which docker > /dev/null; then
	echo "docker needs to be installed"
	exit 1
fi

: ${TEST_IMAGE:?"Need to set TEST_IMAGE, e.g. gcr.io/coreos-k8s-scale-testing/etcd-operator-tests"}

echo "building test-pod container..."
docker build --tag "${TEST_IMAGE}" -f test/pod/Dockerfile . 1>/dev/null

# For gcr users, do "gcloud docker -a" to have access.
echo "pushing test-pod container..."
docker push "${TEST_IMAGE}"
