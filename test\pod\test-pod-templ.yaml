apiVersion: v1
kind: Pod
metadata:
  name: <POD_NAME>
spec:
  restartPolicy: Never
  containers:
  - name: <POD_NAME>
    image: <TEST_IMAGE>
    imagePullPolicy: Always
    command: ["hack/test"]
    resources:
      requests:
        cpu: 2
    env:
      - name: TEST_NAMESPACE
        valueFrom:
          fieldRef:
            fieldPath: metadata.namespace
      - name: PASSES
        value: <PASSES>
      - name: OPERATOR_IMAGE
        value: <OPERATOR_IMAGE>
      - name: E2E_TEST_SELECTOR
        value: <E2E_TEST_SELECTOR>
      - name: TEST_S3_BUCKET
        value: <TEST_S3_BUCKET>
      - name: TEST_AWS_SECRET
        value: <TEST_AWS_SECRET>
      - name: PARALLEL_TEST
        value: "true"
      - name: UPGRADE_TEST_SELECTOR
        value: <UPGRADE_TEST_SELECTOR>
      - name: UPGRADE_FROM
        value: <UPGRADE_FROM>
      - name: UPGRADE_TO
        value: <UPGRADE_TO>
