// Copyright 2017 The etcd-operator Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package backupapi

import (
	"net/url"
	"path"
)

const (
	APIV1 = "/v1"
)

// BackupURLForRestore creates a URL struct for retrieving an existing backup specified by a restore CR
func BackupURLForRestore(scheme, host, restoreName string) *url.URL {
	return &url.URL{
		Scheme: scheme,
		Host:   host,
		Path:   path.Join(APIV1, "backup", restoreName),
	}
}
