#!/usr/bin/env bash

if ! which docker > /dev/null; then
	echo "docker needs to be installed"
	exit 1
fi

: ${IMAGE:?"Need to set IMAGE, e.g. gcr.io/coreos-k8s-scale-testing/etcd-operator"}

echo "building container..."
docker build --tag "${IMAGE}" -f hack/build/Dockerfile . 1>/dev/null

# For gcr users, do "gcloud docker -a" to have access.

echo "pushing container..."
docker push "${IMAGE}" 1>/dev/null
