# This file is autogenerated, do not edit; changes may be undone by the next 'dep ensure'.


[[projects]]
  digest = "1:15eddd48bdb90c4a60b6e8ace553b9b77c95be44f9da53094f980faf2d63809f"
  name = "cloud.google.com/go"
  packages = [
    "compute/metadata",
    "iam",
    "internal",
    "internal/optional",
    "internal/version",
    "storage",
  ]
  pruneopts = "NT"
  revision = "20d4028b8a750c2aca76bf9fefa8ed2d0109b573"
  version = "v0.19.0"

[[projects]]
  digest = "1:ae56bc9c0a00f37a520a61b3a2c127a4930b4586924f3a0026f6220b5869c2bf"
  name = "github.com/Azure/azure-sdk-for-go"
  packages = ["storage"]
  pruneopts = "NT"
  revision = "2d1d76c9013c4feb6695a2346f0e66ea0ef77aa6"
  version = "v11.3.0-beta"

[[projects]]
  digest = "1:b54b11cad2551f2cb74be002a66bc498e91da7f5fa3edd23aea404e774ad0e61"
  name = "github.com/Azure/go-autorest"
  packages = [
    "autorest",
    "autorest/adal",
    "autorest/azure",
    "autorest/date",
    "logger",
    "version",
  ]
  pruneopts = "NT"
  revision = "4b7f49dc5db2e1e6d528524d269b4181981a7ebf"
  version = "v11.1.1"

[[projects]]
  digest = "1:0a111edd8693fd977f42a0c4f199a0efb13c20aec9da99ad8830c7bb6a87e8d6"
  name = "github.com/PuerkitoBio/purell"
  packages = ["."]
  pruneopts = "NT"
  revision = "44968752391892e1b0d0b821ee79e9a85fa13049"
  version = "v1.1.1"

[[projects]]
  branch = "master"
  digest = "1:8098cd40cd09879efbf12e33bcd51ead4a66006ac802cd563a66c4f3373b9727"
  name = "github.com/PuerkitoBio/urlesc"
  packages = ["."]
  pruneopts = "NT"
  revision = "de5bf2ad457846296e2031421a34e2568e304e35"

[[projects]]
  digest = "1:9a6d75376aa0c967c34dda295d1901fea4c2b29db11d21f9ea50ce81e8cca39e"
  name = "github.com/aliyun/aliyun-oss-go-sdk"
  packages = ["oss"]
  pruneopts = "NT"
  revision = "2b29687e15f2cc71fb3a50f28be1d6d30c40c86a"
  version = "1.9.4"

[[projects]]
  digest = "1:1e47beabb90c1e4cc35a3b9ee2d43c191930940363d6c02d2c59acd38c07cef6"
  name = "github.com/aws/aws-sdk-go"
  packages = [
    "aws",
    "aws/awserr",
    "aws/awsutil",
    "aws/client",
    "aws/client/metadata",
    "aws/corehandlers",
    "aws/credentials",
    "aws/credentials/ec2rolecreds",
    "aws/credentials/endpointcreds",
    "aws/credentials/stscreds",
    "aws/defaults",
    "aws/ec2metadata",
    "aws/endpoints",
    "aws/request",
    "aws/session",
    "aws/signer/v4",
    "internal/sdkrand",
    "internal/shareddefaults",
    "private/protocol",
    "private/protocol/query",
    "private/protocol/query/queryutil",
    "private/protocol/rest",
    "private/protocol/restxml",
    "private/protocol/xml/xmlutil",
    "service/s3",
    "service/s3/s3iface",
    "service/s3/s3manager",
    "service/sts",
  ]
  pruneopts = "NT"
  revision = "aace5875a5c3b85a3902c6d72b9caed301d64cce"
  version = "v1.13.8"

[[projects]]
  branch = "master"
  digest = "1:c819830f4f5ef85874a90ac3cbcc96cd322c715f5c96fbe4722eacd3dafbaa07"
  name = "github.com/beorn7/perks"
  packages = ["quantile"]
  pruneopts = "NT"
  revision = "3a771d992973f24aa725d07868b467d1ddfceafb"

[[projects]]
  digest = "1:3e8acd4d7be0f774f424c9f7510597cf711c3782dae2d6a9b94a0d6a856c5648"
  name = "github.com/coreos/etcd"
  packages = [
    "auth/authpb",
    "clientv3",
    "etcdserver/api/v3rpc/rpctypes",
    "etcdserver/etcdserverpb",
    "mvcc/mvccpb",
    "pkg/tlsutil",
    "pkg/transport",
  ]
  pruneopts = "NT"
  revision = "95a726a27e09030f9ccbd9982a1508f5a6d25ada"
  version = "v3.2.13"

[[projects]]
  digest = "1:4b8b5811da6970495e04d1f4e98bb89518cc3cfc3b3f456bdb876ed7b6c74049"
  name = "github.com/davecgh/go-spew"
  packages = ["spew"]
  pruneopts = "NT"
  revision = "8991bc29aa16c548c550c7ff78260e27b9ab7c73"
  version = "v1.1.1"

[[projects]]
  digest = "1:3a5601f7ac7fc10fc2a98039b18d83a767f3830141a08c19ae45c45244f43e57"
  name = "github.com/dgrijalva/jwt-go"
  packages = ["."]
  pruneopts = "NT"
  revision = "06ea1031745cb8b3dab3f6a236daf2b0aa468b7e"
  version = "v3.2.0"

[[projects]]
  digest = "1:2453249730493850718f891fb40b8f1bc932a0265384fc85b269dc04a01d4673"
  name = "github.com/emicklei/go-restful"
  packages = [
    ".",
    "log",
  ]
  pruneopts = "NT"
  revision = "85d198d05a92d31823b852b4a5928114912e8949"
  version = "v2.9.0"

[[projects]]
  digest = "1:81466b4218bf6adddac2572a30ac733a9255919bc2f470b4827a317bd4ee1756"
  name = "github.com/ghodss/yaml"
  packages = ["."]
  pruneopts = "NT"
  revision = "0ca9ea5df5451ffdf184b4428c902747c2c11cd7"
  version = "v1.0.0"

[[projects]]
  digest = "1:d74ba1aea6244ead12e4f16d5f61a15ced9a2f2d1cae2021fbb76088b27e7afa"
  name = "github.com/go-ini/ini"
  packages = ["."]
  pruneopts = "NT"
  revision = "c85607071cf08ca1adaf48319cd1aa322e81d8c1"
  version = "v1.42.0"

[[projects]]
  digest = "1:260f7ebefc63024c8dfe2c9f1a2935a89fa4213637a1f522f592f80c001cc441"
  name = "github.com/go-openapi/jsonpointer"
  packages = ["."]
  pruneopts = "NT"
  revision = "ef5f0afec364d3b9396b7b77b43dbe26bf1f8004"
  version = "v0.18.0"

[[projects]]
  digest = "1:98abd61947ff5c7c6fcfec5473d02a4821ed3a2dd99a4fbfdb7925b0dd745546"
  name = "github.com/go-openapi/jsonreference"
  packages = ["."]
  pruneopts = "NT"
  revision = "8483a886a90412cd6858df4ea3483dce9c8e35a3"
  version = "v0.18.0"

[[projects]]
  digest = "1:4da4ea0a664ba528965683d350f602d0f11464e6bb2e17aad0914723bc25d163"
  name = "github.com/go-openapi/spec"
  packages = ["."]
  pruneopts = "NT"
  revision = "5b6cdde3200976e3ecceb2868706ee39b6aff3e4"
  version = "v0.18.0"

[[projects]]
  digest = "1:dc0f590770e5a6c70ea086232324f7b7dc4857c60eca63ab8ff78e0a5cfcdbf3"
  name = "github.com/go-openapi/swag"
  packages = ["."]
  pruneopts = "NT"
  revision = "1d29f06aebd59ccdf11ae04aa0334ded96e2d909"
  version = "v0.18.0"

[[projects]]
  digest = "1:0b39706cfa32c1ba9e14435b5844d04aef81b60f44b6077e61e0607d56692603"
  name = "github.com/gogo/protobuf"
  packages = [
    "proto",
    "sortkeys",
  ]
  pruneopts = "NT"
  revision = "ba06b47c162d49f2af050fb4c75bcbc86a159d5c"
  version = "v1.2.1"

[[projects]]
  branch = "master"
  digest = "1:e2b86e41f3d669fc36b50d31d32d22c8ac656c75aa5ea89717ce7177e134ff2a"
  name = "github.com/golang/glog"
  packages = ["."]
  pruneopts = "NT"
  revision = "23def4e6c14b4da8ac2ed8007337bc5eb5007998"

[[projects]]
  branch = "master"
  digest = "1:20b774dcfdf0fff3148432beb828c52404f3eb3d70b7ce71ae0356ed6cbc2bae"
  name = "github.com/golang/groupcache"
  packages = ["lru"]
  pruneopts = "NT"
  revision = "5b532d6fd5efaf7fa130d4e859a2fde0fc3a9e1b"

[[projects]]
  digest = "1:2bac1ab777481a7ef169837f0eeac25a1a6628ac93aef853168681dfec4a653c"
  name = "github.com/golang/protobuf"
  packages = [
    "proto",
    "protoc-gen-go/descriptor",
    "ptypes",
    "ptypes/any",
    "ptypes/duration",
    "ptypes/timestamp",
  ]
  pruneopts = "NT"
  revision = "c823c79ea1570fb5ff454033735a8e68575d1d0f"
  version = "v1.3.0"

[[projects]]
  branch = "master"
  digest = "1:05f95ffdfcf651bdb0f05b40b69e7f5663047f8da75c72d58728acb59b5cc107"
  name = "github.com/google/btree"
  packages = ["."]
  pruneopts = "NT"
  revision = "4030bb1f1f0c35b30ca7009e9ebd06849dd45306"

[[projects]]
  branch = "master"
  digest = "1:52c5834e2bebac9030c97cc0798ac11c3aa8a39f098aeb419f142533da6cd3cc"
  name = "github.com/google/gofuzz"
  packages = ["."]
  pruneopts = "NT"
  revision = "24818f796faf91cd76ec7bddd72458fbced7a6c1"

[[projects]]
  digest = "1:fd4d1f4c2d75aee3833ee7d8ef11fcf42ddec3c63d1819548288c3d868d6eb14"
  name = "github.com/googleapis/gax-go"
  packages = [
    ".",
    "v2",
  ]
  pruneopts = "NT"
  revision = "c8a15bac9b9fe955bd9f900272f9a306465d28cf"
  version = "v2.0.3"

[[projects]]
  digest = "1:289332c13b80edfefc88397cce5266c16845dcf204fa2f6ac7e464ee4c7f6e96"
  name = "github.com/googleapis/gnostic"
  packages = [
    "OpenAPIv2",
    "compiler",
    "extensions",
  ]
  pruneopts = "NT"
  revision = "7c663266750e7d82587642f65e60bc4083f1f84e"
  version = "v0.2.0"

[[projects]]
  branch = "master"
  digest = "1:bb7bd892abcb75ef819ce2efab9d54d22b7e38dc05ffac55428bb0578b52912b"
  name = "github.com/gregjones/httpcache"
  packages = [
    ".",
    "diskcache",
  ]
  pruneopts = "NT"
  revision = "3befbb6ad0cc97d4c25d851e9528915809e1a22f"

[[projects]]
  digest = "1:b42cde0e1f3c816dd57f57f7bbcf05ca40263ad96f168714c130c611fc0856a6"
  name = "github.com/hashicorp/golang-lru"
  packages = [
    ".",
    "simplelru",
  ]
  pruneopts = "NT"
  revision = "20f1fb78b0740ba8c3cb143a61e86ba5c8669768"
  version = "v0.5.0"

[[projects]]
  digest = "1:aaa38889f11896ee3644d77e17dc7764cc47f5f3d3b488268df2af2b52541c5f"
  name = "github.com/imdario/mergo"
  packages = ["."]
  pruneopts = "NT"
  revision = "7c29201646fa3de8506f701213473dd407f19646"
  version = "v0.3.7"

[[projects]]
  digest = "1:942ed645ab724049d8b60e361a3a6535b3aad17ddb226e115d2bffeda01e6868"
  name = "github.com/jmespath/go-jmespath"
  packages = ["."]
  pruneopts = "NT"
  revision = "0b12d6b5"

[[projects]]
  digest = "1:1d39c063244ad17c4b18e8da1551163b6ffb52bd1640a49a8ec5c3b7bf4dbd5d"
  name = "github.com/json-iterator/go"
  packages = ["."]
  pruneopts = "NT"
  revision = "1624edc4454b8682399def8740d46db5e4362ba4"
  version = "v1.1.5"

[[projects]]
  branch = "master"
  digest = "1:4925ec3736ef6c299cfcf61597782e3d66ec13114f7476019d04c742a7be55d0"
  name = "github.com/mailru/easyjson"
  packages = [
    "buffer",
    "jlexer",
    "jwriter",
  ]
  pruneopts = "NT"
  revision = "6243d8e04c3f819e79757e8bc3faa15c3cb27003"

[[projects]]
  digest = "1:ea1db000388d88b31db7531c83016bef0d6db0d908a07794bfc36aca16fbf935"
  name = "github.com/matttproud/golang_protobuf_extensions"
  packages = ["pbutil"]
  pruneopts = "NT"
  revision = "c12348ce28de40eed0136aa2b644d0ee0650e56c"
  version = "v1.0.1"

[[projects]]
  digest = "1:2f42fa12d6911c7b7659738758631bec870b7e9b4c6be5444f963cdcfccc191f"
  name = "github.com/modern-go/concurrent"
  packages = ["."]
  pruneopts = "NT"
  revision = "bacd9c7ef1dd9b15be4a9909b8ac7a4e313eec94"
  version = "1.0.3"

[[projects]]
  digest = "1:c6aca19413b13dc59c220ad7430329e2ec454cc310bc6d8de2c7e2b93c18a0f6"
  name = "github.com/modern-go/reflect2"
  packages = ["."]
  pruneopts = "NT"
  revision = "4b7aa43c6742a2c18fdef89dd197aaae7dac7ccd"
  version = "1.0.1"

[[projects]]
  digest = "1:cce3a18fb0b96b5015cd8ca03a57d20a662679de03c4dc4b6ff5f17ea2050fa6"
  name = "github.com/pborman/uuid"
  packages = ["."]
  pruneopts = "NT"
  revision = "e790cca94e6cc75c7064b1332e63811d4aae1a53"
  version = "v1.1"

[[projects]]
  branch = "master"
  digest = "1:bf2ac97824a7221eb16b096aecc1c390d4c8a4e49524386aaa2e2dd215cbfb31"
  name = "github.com/petar/GoLLRB"
  packages = ["llrb"]
  pruneopts = "NT"
  revision = "53be0d36a84c2a886ca057d34b6aa4468df9ccb4"

[[projects]]
  digest = "1:e4e9e026b8e4c5630205cd0208efb491b40ad40552e57f7a646bb8a46896077b"
  name = "github.com/peterbourgon/diskv"
  packages = ["."]
  pruneopts = "NT"
  revision = "5f041e8faa004a95c88a202771f4cc3e991971e6"
  version = "v2.0.1"

[[projects]]
  digest = "1:5cf3f025cbee5951a4ee961de067c8a89fc95a5adabead774f82822efabab121"
  name = "github.com/pkg/errors"
  packages = ["."]
  pruneopts = "NT"
  revision = "645ef00459ed84a119197bfb8d8205042c6df63d"
  version = "v0.8.0"

[[projects]]
  digest = "1:a22debd993d18b9203c985810f33f52e7583724800ee8d15e7d362e7175639d1"
  name = "github.com/prometheus/client_golang"
  packages = ["prometheus"]
  pruneopts = "NT"
  revision = "c5b7fccd204277076155f10851dad72b76a49317"
  version = "v0.8.0"

[[projects]]
  branch = "master"
  digest = "1:c2cc5049e927e2749c0d5163c9f8d924880d83e84befa732b9aad0b6be227bed"
  name = "github.com/prometheus/client_model"
  packages = ["go"]
  pruneopts = "NT"
  revision = "fd36f4220a901265f90734c3183c5f0c91daa0b8"

[[projects]]
  digest = "1:30261b5e263b5c4fb40571b53a41a99c96016c6b1b2c45c1cefd226fc3f6304b"
  name = "github.com/prometheus/common"
  packages = [
    "expfmt",
    "internal/bitbucket.org/ww/goautoneg",
    "model",
  ]
  pruneopts = "NT"
  revision = "cfeb6f9992ffa54aaa4f2170ade4067ee478b250"
  version = "v0.2.0"

[[projects]]
  branch = "master"
  digest = "1:1c282f5c094061ce301d1ea3098799fc907ac1399e9f064c463787323a7b7340"
  name = "github.com/prometheus/procfs"
  packages = [
    ".",
    "internal/util",
    "iostats",
    "nfs",
    "xfs",
  ]
  pruneopts = "NT"
  revision = "6ed1f7e1041181781dd2826d3001075d011a80cc"

[[projects]]
  digest = "1:6bc0652ea6e39e22ccd522458b8bdd8665bf23bdc5a20eec90056e4dc7e273ca"
  name = "github.com/satori/uuid"
  packages = ["."]
  pruneopts = "NT"
  revision = "f58768cc1a7a7e77a3bd49e98cdd21419399b6a3"
  version = "v1.2.0"

[[projects]]
  digest = "1:87d2486e70b24aefe104d9ce5cdb0928385d86f5763ec938cc4d4c03a8a7724d"
  name = "github.com/sirupsen/logrus"
  packages = ["."]
  pruneopts = "NT"
  revision = "d682213848ed68c0a260ca37d6dd5ace8423f5ba"
  version = "v1.0.4"

[[projects]]
  digest = "1:9d8420bbf131d1618bde6530af37c3799340d3762cc47210c1d9532a4c3a2779"
  name = "github.com/spf13/pflag"
  packages = ["."]
  pruneopts = "NT"
  revision = "298182f68c66c05229eb03ac171abe6e309ee79a"
  version = "v1.0.3"

[[projects]]
  digest = "1:7fc6cc3a68672705fa89a37ddc75700f99ea458304f622a9decb358b46f04b18"
  name = "go.opencensus.io"
  packages = [
    ".",
    "exemplar",
    "internal",
    "internal/tagencoding",
    "plugin/ochttp",
    "plugin/ochttp/propagation/b3",
    "stats",
    "stats/internal",
    "stats/view",
    "tag",
    "trace",
    "trace/internal",
    "trace/propagation",
    "trace/tracestate",
  ]
  pruneopts = "NT"
  revision = "2b5032d79456124f42db6b7eb19ac6c155449dc2"
  version = "v0.19.0"

[[projects]]
  branch = "master"
  digest = "1:b19fb19351db5de242e3f1203e63c207c69bf4f4df4822b4ef15220e0204e0e4"
  name = "golang.org/x/crypto"
  packages = ["ssh/terminal"]
  pruneopts = "NT"
  revision = "215aa809caaf1f5be699aef5e3ccebeb15d67b0b"

[[projects]]
  branch = "master"
  digest = "1:7514a961568b6f5d2a114b82de007f4714a27c65dffc71433fe8620a8a9db708"
  name = "golang.org/x/net"
  packages = [
    "context",
    "context/ctxhttp",
    "http/httpguts",
    "http2",
    "http2/hpack",
    "idna",
    "internal/timeseries",
    "trace",
  ]
  pruneopts = "NT"
  revision = "c95aed5357e77a4bf7d3955c46740000a17adee1"

[[projects]]
  branch = "master"
  digest = "1:3418b2325cb0e56bb54774c4e484a2d8829b1fe4bad1c5f077cfd08d138a9a9f"
  name = "golang.org/x/oauth2"
  packages = [
    ".",
    "google",
    "internal",
    "jws",
    "jwt",
  ]
  pruneopts = "NT"
  revision = "e64efc72b421e893cbf63f17ba2221e7d6d0b0f3"

[[projects]]
  branch = "master"
  digest = "1:90abfd79711e2d0ce66e6d23a1b652f8e16c76e12a2ef4b255d1bf0ff4f254b8"
  name = "golang.org/x/sys"
  packages = [
    "unix",
    "windows",
  ]
  pruneopts = "NT"
  revision = "775f8194d0f9e65c46913c7be783d3d95a29333c"

[[projects]]
  digest = "1:8c74f97396ed63cc2ef04ebb5fc37bb032871b8fd890a25991ed40974b00cd2a"
  name = "golang.org/x/text"
  packages = [
    "collate",
    "collate/build",
    "internal/colltab",
    "internal/gen",
    "internal/tag",
    "internal/triegen",
    "internal/ucd",
    "language",
    "secure/bidirule",
    "transform",
    "unicode/bidi",
    "unicode/cldr",
    "unicode/norm",
    "unicode/rangetable",
    "width",
  ]
  pruneopts = "NT"
  revision = "f21a4dfb5e38f5895301dc265a8def02365cc3d0"
  version = "v0.3.0"

[[projects]]
  branch = "master"
  digest = "1:9fdc2b55e8e0fafe4b41884091e51e77344f7dc511c5acedcfd98200003bff90"
  name = "golang.org/x/time"
  packages = ["rate"]
  pruneopts = "NT"
  revision = "85acf8d2951cb2a3bde7632f9ff273ef0379bcbd"

[[projects]]
  branch = "master"
  digest = "1:c8cc5b1665f3678c7958e86c48b1fea26aadc060901fbe3fbaa444100a278196"
  name = "golang.org/x/tools"
  packages = [
    "go/ast/astutil",
    "go/gcexportdata",
    "go/internal/cgo",
    "go/internal/gcimporter",
    "go/internal/packagesdriver",
    "go/packages",
    "go/types/typeutil",
    "imports",
    "internal/fastwalk",
    "internal/gopathwalk",
    "internal/module",
    "internal/semver",
  ]
  pruneopts = "NT"
  revision = "8dcc6e70cdefe9a82236b6e195e4f4e2108fcb9f"

[[projects]]
  digest = "1:acd71c0a3531d4a86d3d1c815398d38e3cc0efdf1f0f6ba8d0a57b6b2222b422"
  name = "google.golang.org/api"
  packages = [
    "gensupport",
    "googleapi",
    "googleapi/internal/uritemplates",
    "googleapi/transport",
    "internal",
    "iterator",
    "option",
    "storage/v1",
    "transport/http",
    "transport/http/internal/propagation",
  ]
  pruneopts = "NT"
  revision = "19e022d8cf43ce81f046bae8cc18c5397cc7732f"
  version = "v0.1.0"

[[projects]]
  digest = "1:902ffa11f1d8c19c12b05cabffe69e1a16608ad03a8899ebcb9c6bde295660ae"
  name = "google.golang.org/appengine"
  packages = [
    ".",
    "internal",
    "internal/app_identity",
    "internal/base",
    "internal/datastore",
    "internal/log",
    "internal/modules",
    "internal/remote_api",
    "internal/urlfetch",
    "urlfetch",
  ]
  pruneopts = "NT"
  revision = "e9657d882bb81064595ca3b56cbe2546bbabf7b1"
  version = "v1.4.0"

[[projects]]
  branch = "master"
  digest = "1:4afad951d1e30852910067075fb28be89122f65377dfbf628bc787306b99b380"
  name = "google.golang.org/genproto"
  packages = [
    "googleapis/api/annotations",
    "googleapis/iam/v1",
    "googleapis/rpc/status",
  ]
  pruneopts = "NT"
  revision = "4f5b463f9597cbe0dd13a6a2cd4f85e788d27508"

[[projects]]
  digest = "1:bd98c07155425154fa0fa9eea73bb70082506633a3d9186ecffa7fb1939f6052"
  name = "google.golang.org/grpc"
  packages = [
    ".",
    "balancer",
    "balancer/base",
    "balancer/roundrobin",
    "codes",
    "connectivity",
    "credentials",
    "encoding",
    "encoding/proto",
    "grpclog",
    "health/grpc_health_v1",
    "internal",
    "internal/backoff",
    "internal/channelz",
    "internal/envconfig",
    "internal/grpcrand",
    "internal/transport",
    "keepalive",
    "metadata",
    "naming",
    "peer",
    "resolver",
    "resolver/dns",
    "resolver/passthrough",
    "stats",
    "status",
    "tap",
  ]
  pruneopts = "NT"
  revision = "32fb0ac620c32ba40a4626ddf94d90d12cce3455"
  version = "v1.14.0"

[[projects]]
  digest = "1:2d1fbdc6777e5408cabeb02bf336305e724b925ff4546ded0fa8715a7267922a"
  name = "gopkg.in/inf.v0"
  packages = ["."]
  pruneopts = "NT"
  revision = "d2d2541c53f18d2a059457998ce2876cc8e67cbf"
  version = "v0.9.1"

[[projects]]
  digest = "1:18108594151654e9e696b27b181b953f9a90b16bf14d253dd1b397b025a1487f"
  name = "gopkg.in/yaml.v2"
  packages = ["."]
  pruneopts = "NT"
  revision = "51d6538a90f86fe93ac480b35f37b2be17fef232"
  version = "v2.2.2"

[[projects]]
  digest = "1:b3f8152a68d73095a40fdcf329a93fc42e8eadb3305171df23fdb6b4e41a6417"
  name = "k8s.io/api"
  packages = [
    "admissionregistration/v1alpha1",
    "admissionregistration/v1beta1",
    "apps/v1",
    "apps/v1beta1",
    "apps/v1beta2",
    "authentication/v1",
    "authentication/v1beta1",
    "authorization/v1",
    "authorization/v1beta1",
    "autoscaling/v1",
    "autoscaling/v2beta1",
    "autoscaling/v2beta2",
    "batch/v1",
    "batch/v1beta1",
    "batch/v2alpha1",
    "certificates/v1beta1",
    "coordination/v1beta1",
    "core/v1",
    "events/v1beta1",
    "extensions/v1beta1",
    "networking/v1",
    "policy/v1beta1",
    "rbac/v1",
    "rbac/v1alpha1",
    "rbac/v1beta1",
    "scheduling/v1alpha1",
    "scheduling/v1beta1",
    "settings/v1alpha1",
    "storage/v1",
    "storage/v1alpha1",
    "storage/v1beta1",
  ]
  pruneopts = "NT"
  revision = "145d52631d00cbfe68490d19ae4f0f501fd31a95"
  version = "kubernetes-1.12.6"

[[projects]]
  digest = "1:82b4765488fd2a8bcefb93e196fdbfe342d33b16ae073a6f51bb4fb13e81e102"
  name = "k8s.io/apiextensions-apiserver"
  packages = [
    "pkg/apis/apiextensions",
    "pkg/apis/apiextensions/v1beta1",
    "pkg/client/clientset/clientset",
    "pkg/client/clientset/clientset/scheme",
    "pkg/client/clientset/clientset/typed/apiextensions/v1beta1",
  ]
  pruneopts = "NT"
  revision = "bd0469a053ff88529a61145790499fe78a09a49d"
  version = "kubernetes-1.12.6"

[[projects]]
  digest = "1:21019b7cf1f77b70d456fbc12a99676f5dfa5123d217fbe83a88822eafc2de98"
  name = "k8s.io/apimachinery"
  packages = [
    "pkg/api/errors",
    "pkg/api/meta",
    "pkg/api/resource",
    "pkg/apis/meta/internalversion",
    "pkg/apis/meta/v1",
    "pkg/apis/meta/v1/unstructured",
    "pkg/apis/meta/v1beta1",
    "pkg/conversion",
    "pkg/conversion/queryparams",
    "pkg/fields",
    "pkg/labels",
    "pkg/runtime",
    "pkg/runtime/schema",
    "pkg/runtime/serializer",
    "pkg/runtime/serializer/json",
    "pkg/runtime/serializer/protobuf",
    "pkg/runtime/serializer/recognizer",
    "pkg/runtime/serializer/streaming",
    "pkg/runtime/serializer/versioning",
    "pkg/selection",
    "pkg/types",
    "pkg/util/cache",
    "pkg/util/clock",
    "pkg/util/diff",
    "pkg/util/errors",
    "pkg/util/framer",
    "pkg/util/intstr",
    "pkg/util/json",
    "pkg/util/mergepatch",
    "pkg/util/naming",
    "pkg/util/net",
    "pkg/util/rand",
    "pkg/util/runtime",
    "pkg/util/sets",
    "pkg/util/strategicpatch",
    "pkg/util/validation",
    "pkg/util/validation/field",
    "pkg/util/wait",
    "pkg/util/yaml",
    "pkg/version",
    "pkg/watch",
    "third_party/forked/golang/json",
    "third_party/forked/golang/reflect",
  ]
  pruneopts = "NT"
  revision = "01f179d85dbce0f2e0e4351a92394b38694b7cae"
  version = "kubernetes-1.12.6"

[[projects]]
  digest = "1:9aef72dc639ffc347ab8b81dc74d85ad12c64708e8bf8cdc43fac17b6018585d"
  name = "k8s.io/client-go"
  packages = [
    "discovery",
    "discovery/fake",
    "kubernetes",
    "kubernetes/fake",
    "kubernetes/scheme",
    "kubernetes/typed/admissionregistration/v1alpha1",
    "kubernetes/typed/admissionregistration/v1alpha1/fake",
    "kubernetes/typed/admissionregistration/v1beta1",
    "kubernetes/typed/admissionregistration/v1beta1/fake",
    "kubernetes/typed/apps/v1",
    "kubernetes/typed/apps/v1/fake",
    "kubernetes/typed/apps/v1beta1",
    "kubernetes/typed/apps/v1beta1/fake",
    "kubernetes/typed/apps/v1beta2",
    "kubernetes/typed/apps/v1beta2/fake",
    "kubernetes/typed/authentication/v1",
    "kubernetes/typed/authentication/v1/fake",
    "kubernetes/typed/authentication/v1beta1",
    "kubernetes/typed/authentication/v1beta1/fake",
    "kubernetes/typed/authorization/v1",
    "kubernetes/typed/authorization/v1/fake",
    "kubernetes/typed/authorization/v1beta1",
    "kubernetes/typed/authorization/v1beta1/fake",
    "kubernetes/typed/autoscaling/v1",
    "kubernetes/typed/autoscaling/v1/fake",
    "kubernetes/typed/autoscaling/v2beta1",
    "kubernetes/typed/autoscaling/v2beta1/fake",
    "kubernetes/typed/autoscaling/v2beta2",
    "kubernetes/typed/autoscaling/v2beta2/fake",
    "kubernetes/typed/batch/v1",
    "kubernetes/typed/batch/v1/fake",
    "kubernetes/typed/batch/v1beta1",
    "kubernetes/typed/batch/v1beta1/fake",
    "kubernetes/typed/batch/v2alpha1",
    "kubernetes/typed/batch/v2alpha1/fake",
    "kubernetes/typed/certificates/v1beta1",
    "kubernetes/typed/certificates/v1beta1/fake",
    "kubernetes/typed/coordination/v1beta1",
    "kubernetes/typed/coordination/v1beta1/fake",
    "kubernetes/typed/core/v1",
    "kubernetes/typed/core/v1/fake",
    "kubernetes/typed/events/v1beta1",
    "kubernetes/typed/events/v1beta1/fake",
    "kubernetes/typed/extensions/v1beta1",
    "kubernetes/typed/extensions/v1beta1/fake",
    "kubernetes/typed/networking/v1",
    "kubernetes/typed/networking/v1/fake",
    "kubernetes/typed/policy/v1beta1",
    "kubernetes/typed/policy/v1beta1/fake",
    "kubernetes/typed/rbac/v1",
    "kubernetes/typed/rbac/v1/fake",
    "kubernetes/typed/rbac/v1alpha1",
    "kubernetes/typed/rbac/v1alpha1/fake",
    "kubernetes/typed/rbac/v1beta1",
    "kubernetes/typed/rbac/v1beta1/fake",
    "kubernetes/typed/scheduling/v1alpha1",
    "kubernetes/typed/scheduling/v1alpha1/fake",
    "kubernetes/typed/scheduling/v1beta1",
    "kubernetes/typed/scheduling/v1beta1/fake",
    "kubernetes/typed/settings/v1alpha1",
    "kubernetes/typed/settings/v1alpha1/fake",
    "kubernetes/typed/storage/v1",
    "kubernetes/typed/storage/v1/fake",
    "kubernetes/typed/storage/v1alpha1",
    "kubernetes/typed/storage/v1alpha1/fake",
    "kubernetes/typed/storage/v1beta1",
    "kubernetes/typed/storage/v1beta1/fake",
    "pkg/apis/clientauthentication",
    "pkg/apis/clientauthentication/v1alpha1",
    "pkg/apis/clientauthentication/v1beta1",
    "pkg/version",
    "plugin/pkg/client/auth/exec",
    "plugin/pkg/client/auth/gcp",
    "rest",
    "rest/watch",
    "testing",
    "third_party/forked/golang/template",
    "tools/auth",
    "tools/cache",
    "tools/clientcmd",
    "tools/clientcmd/api",
    "tools/clientcmd/api/latest",
    "tools/clientcmd/api/v1",
    "tools/leaderelection",
    "tools/leaderelection/resourcelock",
    "tools/metrics",
    "tools/pager",
    "tools/record",
    "tools/reference",
    "tools/watch",
    "transport",
    "util/buffer",
    "util/cert",
    "util/connrotation",
    "util/flowcontrol",
    "util/homedir",
    "util/integer",
    "util/jsonpath",
    "util/retry",
    "util/workqueue",
  ]
  pruneopts = "NT"
  revision = "78295b709ec6fa5be12e35892477a326dea2b5d3"
  version = "kubernetes-1.12.6"

[[projects]]
  digest = "1:26b81b5e76e3f84ea5140da4f74649576e470f79091d2ef8e0d1b5000bc636ca"
  name = "k8s.io/code-generator"
  packages = [
    "cmd/client-gen",
    "cmd/client-gen/args",
    "cmd/client-gen/generators",
    "cmd/client-gen/generators/fake",
    "cmd/client-gen/generators/scheme",
    "cmd/client-gen/generators/util",
    "cmd/client-gen/path",
    "cmd/client-gen/types",
    "cmd/conversion-gen",
    "cmd/conversion-gen/args",
    "cmd/conversion-gen/generators",
    "cmd/deepcopy-gen",
    "cmd/deepcopy-gen/args",
    "cmd/defaulter-gen",
    "cmd/defaulter-gen/args",
    "cmd/informer-gen",
    "cmd/informer-gen/args",
    "cmd/informer-gen/generators",
    "cmd/lister-gen",
    "cmd/lister-gen/args",
    "cmd/lister-gen/generators",
    "cmd/openapi-gen",
    "cmd/openapi-gen/args",
    "pkg/util",
  ]
  pruneopts = "T"
  revision = "b1289fc74931d4b6b04bd1a259acfc88a2cb0a66"
  version = "kubernetes-1.12.6"

[[projects]]
  branch = "master"
  digest = "1:4e07c417d966628ee9e471354ad5e311c9c25ff357d42fd0dd2f81a40caf6aad"
  name = "k8s.io/gengo"
  packages = [
    "args",
    "examples/deepcopy-gen/generators",
    "examples/defaulter-gen/generators",
    "examples/set-gen/sets",
    "generator",
    "namer",
    "parser",
    "types",
  ]
  pruneopts = "NT"
  revision = "0689ccc1d7d65d9dd1bedcc3b0b1ed7df91ba266"

[[projects]]
  digest = "1:29f93bb84d907a2c035e729e19d66fe52165d8c905cb3ef1920140d76ae6afaf"
  name = "k8s.io/klog"
  packages = ["."]
  pruneopts = "NT"
  revision = "71442cd4037d612096940ceb0f3fec3f7fff66e0"
  version = "v0.2.0"

[[projects]]
  branch = "master"
  digest = "1:4dbb9fa9ab4548516c842af1280f967b174f0e40590f37eee3451bba8de8d3d5"
  name = "k8s.io/kube-openapi"
  packages = [
    "cmd/openapi-gen/args",
    "pkg/common",
    "pkg/generators",
    "pkg/generators/rules",
    "pkg/util/proto",
    "pkg/util/sets",
  ]
  pruneopts = "NT"
  revision = "d50a959ae76a85c7c262a9767ef29f37093c2b8a"

[solve-meta]
  analyzer-name = "dep"
  analyzer-version = 1
  input-imports = [
    "cloud.google.com/go/storage",
    "github.com/Azure/azure-sdk-for-go/storage",
    "github.com/Azure/go-autorest/autorest/azure",
    "github.com/aliyun/aliyun-oss-go-sdk/oss",
    "github.com/aws/aws-sdk-go/aws",
    "github.com/aws/aws-sdk-go/aws/session",
    "github.com/aws/aws-sdk-go/service/s3",
    "github.com/aws/aws-sdk-go/service/s3/s3manager",
    "github.com/coreos/etcd/clientv3",
    "github.com/coreos/etcd/etcdserver/api/v3rpc/rpctypes",
    "github.com/coreos/etcd/etcdserver/etcdserverpb",
    "github.com/coreos/etcd/pkg/transport",
    "github.com/pborman/uuid",
    "github.com/pkg/errors",
    "github.com/prometheus/client_golang/prometheus",
    "github.com/sirupsen/logrus",
    "golang.org/x/oauth2",
    "golang.org/x/time/rate",
    "google.golang.org/api/iterator",
    "google.golang.org/api/option",
    "k8s.io/api/apps/v1beta1",
    "k8s.io/api/core/v1",
    "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1",
    "k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset",
    "k8s.io/apimachinery/pkg/api/errors",
    "k8s.io/apimachinery/pkg/api/resource",
    "k8s.io/apimachinery/pkg/apis/meta/v1",
    "k8s.io/apimachinery/pkg/fields",
    "k8s.io/apimachinery/pkg/labels",
    "k8s.io/apimachinery/pkg/runtime",
    "k8s.io/apimachinery/pkg/runtime/schema",
    "k8s.io/apimachinery/pkg/runtime/serializer",
    "k8s.io/apimachinery/pkg/types",
    "k8s.io/apimachinery/pkg/util/intstr",
    "k8s.io/apimachinery/pkg/util/rand",
    "k8s.io/apimachinery/pkg/util/runtime",
    "k8s.io/apimachinery/pkg/util/strategicpatch",
    "k8s.io/apimachinery/pkg/util/wait",
    "k8s.io/apimachinery/pkg/watch",
    "k8s.io/client-go/discovery",
    "k8s.io/client-go/discovery/fake",
    "k8s.io/client-go/kubernetes",
    "k8s.io/client-go/kubernetes/fake",
    "k8s.io/client-go/kubernetes/scheme",
    "k8s.io/client-go/kubernetes/typed/core/v1",
    "k8s.io/client-go/plugin/pkg/client/auth/gcp",
    "k8s.io/client-go/rest",
    "k8s.io/client-go/testing",
    "k8s.io/client-go/tools/cache",
    "k8s.io/client-go/tools/clientcmd",
    "k8s.io/client-go/tools/leaderelection",
    "k8s.io/client-go/tools/leaderelection/resourcelock",
    "k8s.io/client-go/tools/record",
    "k8s.io/client-go/tools/watch",
    "k8s.io/client-go/util/flowcontrol",
    "k8s.io/client-go/util/workqueue",
    "k8s.io/code-generator/cmd/client-gen",
    "k8s.io/code-generator/cmd/conversion-gen",
    "k8s.io/code-generator/cmd/deepcopy-gen",
    "k8s.io/code-generator/cmd/defaulter-gen",
    "k8s.io/code-generator/cmd/informer-gen",
    "k8s.io/code-generator/cmd/lister-gen",
    "k8s.io/code-generator/cmd/openapi-gen",
    "k8s.io/gengo/args",
  ]
  solver-name = "gps-cdcl"
  solver-version = 1
