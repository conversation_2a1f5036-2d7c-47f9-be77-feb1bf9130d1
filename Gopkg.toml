# Force dep to vendor the code generators, which aren't imported just used at dev time.
required = [
  "k8s.io/code-generator/cmd/defaulter-gen",
  "k8s.io/code-generator/cmd/deepcopy-gen",
  "k8s.io/code-generator/cmd/conversion-gen",
  "k8s.io/code-generator/cmd/client-gen",
  "k8s.io/code-generator/cmd/lister-gen",
  "k8s.io/code-generator/cmd/informer-gen",
  "k8s.io/code-generator/cmd/openapi-gen",
  "k8s.io/gengo/args",
]

[[override]]
  name = "k8s.io/code-generator"
  version = "kubernetes-1.12.6"

[[override]]
  name = "k8s.io/api"
  version = "kubernetes-1.12.6"

[[override]]
  name = "k8s.io/apiextensions-apiserver"
  version = "kubernetes-1.12.6"

[[override]]
  name = "k8s.io/apimachinery"
  version = "kubernetes-1.12.6"

[[override]]
  name = "k8s.io/client-go"
  version = "kubernetes-1.12.6"

[[constraint]]
  name = "github.com/coreos/etcd"
  version = "=3.2.13"

[[override]]
  name = "google.golang.org/grpc"
  version = "=1.14.0"

[[constraint]]
  name = "github.com/aws/aws-sdk-go"
  version = "=1.13.8"

[[constraint]]
  name = "github.com/pborman/uuid"
  version = "=1.1"

[[constraint]]
  name = "github.com/pkg/errors"
  version = "=0.8.0"

[[constraint]]
  name = "github.com/prometheus/client_golang"
  version = "=0.8.0"

[[constraint]]
  name = "github.com/sirupsen/logrus"
  version = "=1.0.4"

[[constraint]]
  name = "github.com/Azure/azure-sdk-for-go"
  version = "=11.3.0-beta"

[[constraint]]
  name = "cloud.google.com/go"
  version = "0.19.0"

[prune]
  go-tests = true
  non-go = true

  [[prune.project]]
    name = "k8s.io/code-generator"
    non-go = false

[[constraint]]
  name = "github.com/aliyun/aliyun-oss-go-sdk"
  version = "=1.9.4"
